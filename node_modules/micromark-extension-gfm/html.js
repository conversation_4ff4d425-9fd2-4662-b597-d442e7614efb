var combine = require('micromark/dist/util/combine-html-extensions')
var autolink = require('micromark-extension-gfm-autolink-literal/html')
var strikethrough = require('micromark-extension-gfm-strikethrough/html')
var table = require('micromark-extension-gfm-table/html')
var tagfilter = require('micromark-extension-gfm-tagfilter/html')
var tasklist = require('micromark-extension-gfm-task-list-item/html')

module.exports = combine([autolink, strikethrough, table, tagfilter, tasklist])
