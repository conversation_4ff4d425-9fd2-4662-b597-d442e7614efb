{"name": "micromark-extension-gfm", "version": "0.3.3", "description": "micromark extension to support GFM (GitHub Flavored Markdown)", "license": "MIT", "keywords": ["micromark", "micromark-extension", "table", "strikethrough", "tasklist", "autolink", "tagfilter", "github", "gfm", "markdown", "unified"], "repository": "micromark/micromark-extension-gfm", "bugs": "https://github.com/micromark/micromark-extension-gfm/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "types": "types/index.d.ts", "files": ["types/*.d.ts", "index.js", "html.js", "syntax.js"], "dependencies": {"micromark": "~2.11.0", "micromark-extension-gfm-autolink-literal": "~0.5.0", "micromark-extension-gfm-strikethrough": "~0.6.5", "micromark-extension-gfm-table": "~0.4.0", "micromark-extension-gfm-tagfilter": "~0.3.0", "micromark-extension-gfm-task-list-item": "~0.3.0"}, "devDependencies": {"dtslint": "^4.0.0", "hast-util-select": "^4.0.0", "hast-util-to-text": "^2.0.0", "node-fetch": "^2.6.1", "nyc": "^15.0.0", "prettier": "^2.0.0", "rehype-parse": "^7.0.1", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "tape": "^5.0.0", "unified": "^9.0.0", "xo": "^0.38.0"}, "scripts": {"format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test/index.js", "test-types": "dtslint types", "test": "npm run format && npm run test-coverage && npm run test-types"}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "ignores": ["types"], "rules": {"guard-for-in": "off", "unicorn/no-array-for-each": "off"}}, "remarkConfig": {"plugins": ["preset-wooorm"]}}