{"name": "mdast-util-gfm-strikethrough", "version": "0.2.3", "description": "mdast extension to parse and serialize GFM strikethrough", "license": "MIT", "keywords": ["unist", "mdast", "mdast-util", "util", "utility", "markdown", "markup", "strikethrough", "strike", "through", "del", "delete", "deletion", "gfm"], "repository": "syntax-tree/mdast-util-gfm-strikethrough", "bugs": "https://github.com/syntax-tree/mdast-util-gfm-strikethrough/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["from-markdown.js", "index.js", "to-markdown.js"], "dependencies": {"mdast-util-to-markdown": "^0.6.0"}, "devDependencies": {"mdast-util-from-markdown": "^0.8.0", "micromark-extension-gfm-strikethrough": "^0.6.0", "nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "tape": "^5.0.0", "unist-util-remove-position": "^3.0.0", "xo": "^0.36.0"}, "scripts": {"format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test.js", "test": "npm run format && npm run test-coverage"}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false}, "remarkConfig": {"plugins": ["preset-wooorm"]}}