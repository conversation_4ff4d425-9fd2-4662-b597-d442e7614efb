{"name": "micromark-extension-gfm-autolink-literal", "version": "0.5.7", "description": "micromark extension to support GFM autolink literals", "license": "MIT", "keywords": ["micromark", "micromark-extension", "literal", "url", "autolink", "auto", "link", "gfm", "markdown", "unified"], "repository": "micromark/micromark-extension-gfm-autolink-literal", "bugs": "https://github.com/micromark/micromark-extension-gfm-autolink-literal/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["index.js", "html.js", "syntax.js"], "dependencies": {"micromark": "~2.11.3"}, "devDependencies": {"nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "tape": "^5.0.0", "xo": "^0.38.0"}, "scripts": {"format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test/index.js", "test": "npm run format && npm run test-coverage"}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "rules": {"unicorn/no-this-assignment": "off"}}, "remarkConfig": {"plugins": ["preset-wooorm"]}}