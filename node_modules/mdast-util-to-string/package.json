{"name": "mdast-util-to-string", "version": "2.0.0", "description": "mdast utility to get the plain text content of a node", "license": "MIT", "keywords": ["unist", "mdast", "mdast-util", "util", "utility", "markdown", "node", "string", "serialize"], "repository": "syntax-tree/mdast-util-to-string", "bugs": "https://github.com/syntax-tree/mdast-util-to-string/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["index.js", "types/index.d.ts"], "types": "types/index.d.ts", "devDependencies": {"browserify": "^17.0.0", "dtslint": "^4.0.0", "nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "tape": "^5.0.0", "tinyify": "^3.0.0", "xo": "^0.34.0"}, "scripts": {"format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "build-bundle": "browserify . -s mdastUtilToString -o mdast-util-to-string.js", "build-mangle": "browserify . -s mdastUtilToString -o mdast-util-to-string.min.js -p tinyify", "build": "npm run build-bundle && npm run build-mangle", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test.js", "test-types": "dtslint types", "test": "npm run format && npm run build && npm run test-coverage && npm run test-types"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "ignore": ["mdast-util-to-string.js", "types/test.ts"]}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "remarkConfig": {"plugins": ["preset-wooorm"]}}