{"name": "mdast-util-find-and-replace", "version": "1.1.1", "description": "mdast utility to find and replace text in a tree", "license": "MIT", "keywords": ["unist", "mdast", "mdast-util", "util", "utility", "markdown", "find", "replace"], "repository": "syntax-tree/mdast-util-find-and-replace", "bugs": "https://github.com/syntax-tree/mdast-util-find-and-replace/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["index.js"], "dependencies": {"escape-string-regexp": "^4.0.0", "unist-util-is": "^4.0.0", "unist-util-visit-parents": "^3.0.0"}, "devDependencies": {"nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "tape": "^5.0.0", "unist-builder": "^2.0.0", "xo": "^0.37.0"}, "scripts": {"format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test.js", "test": "npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "rules": {"unicorn/prefer-type-error": "off", "guard-for-in": "off"}}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "remarkConfig": {"plugins": ["preset-wooorm"]}}