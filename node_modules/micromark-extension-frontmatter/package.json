{"name": "micromark-extension-frontmatter", "version": "0.2.2", "description": "micromark extension to support frontmatter (YAML, TOML, etc)", "license": "MIT", "keywords": ["micromark", "micromark-extension", "frontmatter", "yaml", "toml", "gfm", "markdown", "unified"], "repository": "micromark/micromark-extension-frontmatter", "bugs": "https://github.com/micromark/micromark-extension-frontmatter/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["lib/", "index.js", "html.js"], "dependencies": {"fault": "^1.0.0"}, "devDependencies": {"micromark": "~2.9.0", "nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^8.0.0", "remark-preset-wooorm": "^7.0.0", "tape": "^5.0.0", "xo": "^0.33.0"}, "scripts": {"format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test.js", "test": "npm run format && npm run test-coverage"}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false}, "remarkConfig": {"plugins": ["preset-wooorm"]}}