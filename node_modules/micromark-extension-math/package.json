{"name": "micromark-extension-math", "version": "0.1.2", "description": "micromark extension to support math (`$C_L$`)", "license": "MIT", "keywords": ["micromark", "micromark-extension", "math", "katex", "latex", "tex", "markdown", "unified"], "repository": "micromark/micromark-extension-math", "bugs": "https://github.com/micromark/micromark-extension-math/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["lib/", "index.js", "html.js"], "dependencies": {"katex": "^0.12.0", "micromark": "~2.11.0"}, "devDependencies": {"nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "tape": "^5.0.0", "xo": "^0.36.0"}, "scripts": {"format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test.js", "test": "npm run format && npm run test-coverage"}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false}, "remarkConfig": {"plugins": ["preset-wooorm"]}}