{
  "linterOptions": {
    "exclude": [
      "**/*.d.ts",
      "**/fonts/tex/*.ts"
    ]
  },
  "rulesDirectory": [
    "node_modules/tslint-jsdoc-rules/lib"
  ],
  "rules": {
    "class-name": true,
    "comment-format": [
      true,
      "check-space"
//      "check-uppercase"
    ],
    "curly": [
      true,
      "ignore-same-line"
    ],
    "eofline": true,
    "indent": [
      true,
      "spaces"
    ],
    "jsdoc-format": true,
    "jsdoc-require": [
      true,
      "no-private-properties"
    ],
    "linebreak-style": true,
    "max-line-length": [
      true,
      140
    ],
    "member-access": true,
    "member-ordering": [
      true,
      {"order": [
        "static-field",
        "instance-field",
        "static-method",
        "instance-method"
      ]}
    ],
    "no-duplicate-variable": true,
    "no-eval": true,
    "no-internal-module": true,
    "no-trailing-whitespace": true,
    "no-var-keyword": true,
    "one-line": [
      true,
      "check-open-brace",
      "check-whitespace"
    ],
    "quotemark": [
      true,
      "single"
    ],
    "semicolon": true,
    "triple-equals": [
      true,
      "allow-null-check"
    ],
    "typedef": [
      "callSignature",
      "catchClause",
      "indexSignature",
      "parameter",
      "propertySignature",
      "variableDeclarator"
    ],
    "typedef-whitespace": [
      true,
      {
        "call-signature": "nospace",
        "index-signature": "nospace",
        "parameter": "nospace",
        "property-declaration": "nospace",
        "variable-declaration": "nospace"
      }
    ],
    "variable-name": [
      true,
      "ban-keywords"
    ],
    "whitespace": [
      true,
      "check-branch",
      "check-decl",
      "check-operator",
      "check-separator",
      "check-type",
      "check-rest-spread",
      "check-type-operator"
    ]
  }
}
