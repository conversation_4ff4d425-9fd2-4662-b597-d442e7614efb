{"name": "normalize-package-data", "version": "3.0.3", "author": "<PERSON><PERSON> <<EMAIL>>", "description": "Normalizes data that can be found in package.json files.", "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/npm/normalize-package-data.git"}, "main": "lib/normalize.js", "scripts": {"postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preversion": "npm test", "test": "tap test/*.js --branches 85 --functions 90 --lines 85 --statements 85", "npmclilint": "npmcli-lint", "lint": "npm run npmclilint -- \"lib/**/*.*js\" \"test/**/*.*js\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint --", "postsnap": "npm run lintfix --"}, "dependencies": {"hosted-git-info": "^4.0.1", "is-core-module": "^2.5.0", "semver": "^7.3.4", "validate-npm-package-license": "^3.0.1"}, "devDependencies": {"@npmcli/lint": "^1.0.2", "tap": "^15.0.9"}, "files": ["lib/*.js", "lib/*.json", "AUTHORS"], "engines": {"node": ">=10"}}