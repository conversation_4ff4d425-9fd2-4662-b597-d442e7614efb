{"name": "micromark-extension-gfm-task-list-item", "version": "0.3.3", "description": "micromark extension to support GFM task list items", "license": "MIT", "keywords": ["micromark", "micromark-extension", "task", "list", "item", "check", "checkbox", "todo", "gfm", "markdown", "unified"], "repository": "micromark/micromark-extension-gfm-task-list-item", "bugs": "https://github.com/micromark/micromark-extension-gfm-task-list-item/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["index.js", "html.js", "syntax.js"], "dependencies": {"micromark": "~2.11.0"}, "devDependencies": {"control-pictures": "^1.0.0", "nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "tape": "^5.0.0", "xo": "^0.36.0"}, "scripts": {"format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test/index.js", "test": "npm run format && npm run test-coverage"}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false}, "remarkConfig": {"plugins": ["preset-wooorm"]}}