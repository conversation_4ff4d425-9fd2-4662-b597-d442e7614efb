{"name": "micromark-extension-gfm-tagfilter", "version": "0.3.0", "description": "micromark extension to support GFM tagfilter", "license": "MIT", "keywords": ["micromark", "micromark-extension", "tagfilter", "tag", "filter", "dangerous", "html", "gfm", "markdown", "unified"], "repository": "micromark/micromark-extension-gfm-tagfilter", "bugs": "https://github.com/micromark/micromark-extension-gfm-tagfilter/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["index.js", "html.js"], "dependencies": {}, "devDependencies": {"micromark": "~2.6.0", "nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^8.0.0", "remark-preset-wooorm": "^7.0.0", "tape": "^5.0.0", "xo": "^0.33.0"}, "scripts": {"format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test/index.js", "test": "npm run format && npm run test-coverage"}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false}, "remarkConfig": {"plugins": ["preset-wooorm"]}}