{"name": "messageformat", "version": "2.3.0", "description": "PluralFormat and SelectFormat Message and i18n Tool - A JavaScript Implemenation of the ICU standards.", "keywords": ["i18n", "pluralformat", "icu", "gettext", "choiceformat", "selectformat", "messageformat", "internationalization"], "author": "<PERSON> <alex<PERSON><EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "homepage": "https://messageformat.github.io/messageformat/", "repository": "messageformat/messageformat", "main": "index.js", "browser": "./messageformat.js", "files": ["index.d.ts", "lib", "messageformat.js", "messageformat.js.map", "messages.js"], "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "6.5.0"}}]], "plugins": [["@babel/plugin-proposal-class-properties", {"loose": true}]]}, "eslintConfig": {"env": {"commonjs": true, "es6": true}, "overrides": [{"files": ["src/*.js"], "parser": "babel-es<PERSON>"}, {"files": ["webpack.config.js"], "env": {"node": true}}]}, "dependencies": {"make-plural": "^4.3.0", "messageformat-formatters": "^2.0.1", "messageformat-parser": "^4.1.2"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/preset-env": "^7.4.5", "babel-loader": "^8.0.6", "webpack": "^4.32.2", "webpack-cli": "^3.3.2"}, "scripts": {"prebuild": "babel src -d lib", "build": "webpack --mode production"}, "gitHead": "cd47ed7db2b7a4f5e21df4cd6284ae3f7dad05a1"}