{"name": "mini-create-react-context", "version": "0.4.1", "description": "Smaller Polyfill for the proposed React context API", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/index.d.ts", "repository": "https://github.com/StringEpsilon/mini-create-react-context", "author": "StringEpsilon", "license": "MIT", "keywords": ["react", "context", "contextTypes", "polyfill", "ponyfill"], "files": ["dist/**"], "scripts": {"test": "jest", "build": "rollup -c rollup.config.js", "prepublish": "npm run build"}, "dependencies": {"@babel/runtime": "^7.12.1", "tiny-warning": "^1.0.3"}, "peerDependencies": {"prop-types": "^15.0.0", "react": "^0.14.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}, "devDependencies": {"@babel/core": "^7.12.3", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/preset-env": "^7.12.1", "@babel/preset-react": "^7.12.1", "@babel/preset-typescript": "^7.12.1", "@types/enzyme": "^3.10.5", "@types/jest": "^26.0.10", "@types/react": "^16.9.46", "@wessberg/rollup-plugin-ts": "^1.3.6", "babel-jest": "^25.5.1", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.5", "enzyme-to-json": "^3.6.1", "jest": "^26.6.0", "prop-types": "^15.6.0", "raf": "^3.4.1", "react": "^16.13.1", "react-dom": "^16.13.1", "rollup": "^2.32.1", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^7.0.2", "typescript": "^4.0.3"}, "jest": {"snapshotSerializers": ["enzyme-to-json/serializer"]}}