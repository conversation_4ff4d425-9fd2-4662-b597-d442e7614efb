{"name": "mdast-util-to-hast", "version": "10.2.0", "description": "mdast utility to transform to hast", "license": "MIT", "keywords": ["unist", "mdast", "mdast-util", "hast", "hast-util", "util", "utility", "markdown", "html"], "repository": "syntax-tree/mdast-util-to-hast", "bugs": "https://github.com/syntax-tree/mdast-util-to-hast/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "files": ["lib", "index.js", "types/index.d.ts"], "types": "types/index.d.ts", "dependencies": {"@types/mdast": "^3.0.0", "@types/unist": "^2.0.0", "mdast-util-definitions": "^4.0.0", "mdurl": "^1.0.0", "unist-builder": "^2.0.0", "unist-util-generated": "^1.0.0", "unist-util-position": "^3.0.0", "unist-util-visit": "^2.0.0"}, "devDependencies": {"browserify": "^17.0.0", "dtslint": "^4.0.0", "nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "tape": "^5.0.0", "tinyify": "^3.0.0", "xo": "^0.37.0"}, "scripts": {"format": "remark . -qfo && prettier -w . --loglevel warn  && xo --fix", "build-bundle": "browserify index.js -s mdastUtilToHast -o mdast-util-to-hast.js", "build-mangle": "browserify index.js -s mdastUtilToHast -o mdast-util-to-hast.min.js -p tinyify", "build": "npm run build-bundle && npm run build-mangle", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test/index.js", "test-types": "dtslint types", "test": "npm run format && npm run build && npm run test-coverage && npm run test-types"}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "ignores": ["**/*.ts", "mdast-util-to-hast.js"], "rules": {"unicorn/explicit-length-check": "off", "unicorn/prefer-includes": "off"}}, "remarkConfig": {"plugins": ["preset-wooorm"]}}