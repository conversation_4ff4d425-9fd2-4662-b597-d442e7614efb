{"name": "micromark-extension-gfm-strikethrough", "version": "0.6.5", "description": "micromark extension to support GFM strikethrough", "license": "MIT", "keywords": ["micromark", "micromark-extension", "strikethrough", "strike", "through", "del", "delete", "deletion", "gfm", "markdown", "unified"], "repository": "micromark/micromark-extension-gfm-strikethrough", "bugs": "https://github.com/micromark/micromark-extension-gfm-strikethrough/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "types": "types/index.d.ts", "files": ["types/*.d.ts", "lib/", "index.js", "html.js"], "dependencies": {"micromark": "~2.11.0"}, "devDependencies": {"dtslint": "^4.0.0", "nyc": "^15.0.0", "prettier": "^2.0.0", "remark-cli": "^9.0.0", "remark-preset-wooorm": "^8.0.0", "tape": "^5.0.0", "xo": "^0.38.0"}, "scripts": {"format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node test", "test-coverage": "nyc --reporter lcov tape test/index.js", "test-types": "dtslint types", "test": "npm run format && npm run test-coverage && npm run test-types"}, "nyc": {"check-coverage": true, "lines": 100, "functions": 100, "branches": 100}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "ignores": ["types"]}, "remarkConfig": {"plugins": ["preset-wooorm"]}}