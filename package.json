{"name": "root", "private": true, "packageManager": "yarn@1.22.22", "devEngines": {"node": "8.x || 9.x || 10.x || 11.x"}, "workspaces": ["packages/*", "formily/*", "examples/*"], "scripts": {"bootstrap": "lerna bootstrap", "clean": "lerna clean", "build": "rimraf -rf packages/*/{lib,esm} && lerna run build", "build:docs": "dumi build", "start": "NODE_OPTIONS=--openssl-legacy-provider dumi dev", "start:playground": "npm run start:basic", "start:basic": "yarn workspace @designable/basic-example start", "start:formily-antd": "yarn workspace @designable/formily-antd start", "start:sandbox": "yarn workspace @designable/sandbox-example start", "start:multi-workspace": "yarn workspace @designable/multi-workspace-example start", "start:sandbox-multi-workspace": "yarn workspace @designable/sandbox-multi-workspace-example start", "test": "jest --coverage", "test:watch": "jest --watch", "test:prod": "jest --coverage --silent", "preversion": "npm run build && npm run lint", "version": "ts-node scripts/release changelog && git add -A", "version:alpha": "lerna version prerelease --preid alpha", "version:beta": "lerna version prerelease --preid beta", "version:rc": "lerna version prerelease --preid rc", "version:patch": "lerna version patch", "version:minor": "lerna version minor", "version:preminor": "lerna version preminor --preid beta", "version:major": "lerna version major", "release:github": "ts-node scripts/release release", "release:force": "lerna publish from-package --yes", "prelease:force": "lerna publish from-package --yes --dist-tag next", "release": "lerna <PERSON>", "prelease": "lerna publish --dist-tag next", "lint": "eslint --ext .ts,.tsx,.js  --fix", "postinstall": "opencollective-postinstall"}, "resolutions": {"@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "yargs": "^16.x"}, "devDependencies": {"@alifd/next": "^1.19.1", "@rollup/plugin-commonjs": "^17.0.0", "@testing-library/jest-dom": "^5.0.0", "@testing-library/react": "^11.2.3", "@types/hoist-non-react-statics": "^3.3.1", "@types/fs-extra": "^8.1.0", "@types/jest": "^24.0.18", "@types/node": "^12.6.8", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/dateformat": "^3.0.1", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.8.2", "@umijs/plugin-sass": "^1.1.1", "antd": "^4.0.0", "chalk": "^2.4.2", "chokidar": "^2.1.2", "concurrently": "^4.1.0", "conventional-commit-types": "^2.2.0", "cool-path": "^1.0.6", "cross-env": "^5.2.0", "css-loader": "^5.0.0", "cz-conventional-changelog": "^2.1.0", "dumi": "^1.1.0-rc.8", "eslint": "^7.14.0", "semver": "^7.3.5", "string-similarity": "^4.0.4", "eslint-config-prettier": "^7.0.0", "eslint-plugin-import": "^2.13.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-promise": "^4.0.0", "eslint-plugin-react": "^7.14.2", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-markdown": "^2.0.1", "execa": "^5.0.0", "file-loader": "^5.0.2", "findup": "^0.1.5", "fs-extra": "^7.0.1", "ghooks": "^2.0.4", "glob": "^7.1.3", "html-webpack-plugin": "^3.2.0", "immutable": "^4.0.0-rc.12", "istanbul-api": "^2.1.1", "istanbul-lib-coverage": "^2.0.3", "jest": "^26.0.0", "jest-codemods": "^0.19.1", "jest-dom": "^3.1.2", "jest-localstorage-mock": "^2.3.0", "jest-styled-components": "6.3.3", "jest-watch-lerna-packages": "^1.1.0", "lerna": "^4.0.0", "gh-release": "^5.0.1", "less": "^4.1.1", "less-loader": "^5.0.0", "less-plugin-npm-import": "^2.1.0", "postcss": "^8.0.0", "postcss-less": "^4.0.0", "lint-staged": "^8.2.1", "mobx": "^6.0.4", "mobx-react-lite": "^3.1.6", "escape-string-regexp": "^4.0.0", "onchange": "^5.2.0", "opencollective": "^1.0.3", "opencollective-postinstall": "^2.0.2", "param-case": "^3.0.4", "prettier": "^2.2.1", "pretty-format": "^24.0.0", "pretty-quick": "^3.1.0", "raw-loader": "^4.0.0", "react": "^17.0.1", "react-dom": "^17.0.1", "react-test-renderer": "^16.11.0", "rimraf": "^3.0.0", "rollup": "^2.37.1", "rollup-plugin-external-globals": "^0.6.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.30.0", "rollup-plugin-postcss": "^4.0.0", "semver-regex": "^2.0.0", "staged-git-files": "^1.1.2", "style-loader": "^1.1.3", "styled-components": "^5.0.0", "ts-import-plugin": "1.6.1", "ts-jest": "^26.0.0", "ts-loader": "^7.0.4", "ts-node": "^9.1.1", "typescript": "^4.1.5", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-dev-server": "^3.10.1", "showdown": "^1.9.1", "react-mde": "^11.5.0"}, "repository": {"type": "git", "url": "git+https://github.com/alibaba/designable.git"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{ts,tsx,js}": ["eslint --ext .ts,.tsx,.js --fix", "pretty-quick --staged", "git add"], "*.md": ["pretty-quick --staged", "git add"]}, "dependencies": {"@ant-design/icons": "^4.0.2"}}