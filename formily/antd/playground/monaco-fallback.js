import React from 'react';

// Simple fallback for Monaco Editor
const Editor = ({ value, onChange, language, height = '400px', width = '100%', ...props }) => {
  return React.createElement('textarea', {
    value: value || '',
    onChange: (e) => onChange && onChange(e.target.value),
    style: {
      width,
      height,
      fontFamily: 'monospace',
      fontSize: '14px',
      border: '1px solid #ccc',
      padding: '8px',
      resize: 'vertical'
    },
    placeholder: `Code editor (${language || 'text'})`,
    ...props
  });
};

// Mock loader
export const loader = {
  init: () => Promise.resolve({}),
  config: () => {}
};

export default Editor;
